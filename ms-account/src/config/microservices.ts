import config from 'app.config.json'

import { MicroserviceClient } from '@thrift/common/engines/Http'

export const personServiceClient = new MicroserviceClient({
  baseURL: config.microservices.person.baseUrl,
  serviceName: 'ms-person',
})

export const walletServiceClient = new MicroserviceClient({
  baseURL: config.microservices.wallet.baseUrl,
  serviceName: 'ms-wallet',
})

export const microserviceClients = {
  person: personServiceClient,
  wallet: walletServiceClient,
}
