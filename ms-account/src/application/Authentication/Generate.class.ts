import { walletServiceClient } from '@app/config/microservices'
import { decodeRefreshToken, generateToken } from '@credentials/authorization'

import { validateWithZod } from '@thrift/common/engines/Validation'

import type {
  AuthenticationResult,
  GenerateTokenProps,
  RefreshTokenProps,
  TokenResult,
  UserProfileResponse,
} from '@app/application/Authentication'
import {
  AuthenticateSchema,
  RefreshTokenSchema,
} from '@app/application/Authentication/Authentication.schema'

import { Account } from '@app/domain/database/Account'
import { Person } from '@app/domain/database/Person'

export class Generate {
  public async authenticateByEmailAndPassword(
    input: unknown,
  ): Promise<AuthenticationResult> {
    const { tenantId, email, password }: GenerateTokenProps = validateWithZod(
      AuthenticateSchema,
      input,
    )

    try {
      await walletServiceClient.get(`/tenants/${tenantId}`)
    } catch {
      throw new ReferenceError('Invalid tenantId')
    }

    const accountModel = new Account()

    const account = await accountModel.findByCredentials({
      email,
      password,
    })

    return {
      accountId: account.id,
      tenantId,
    }
  }

  public async getUserProfile(accountId: string): Promise<UserProfileResponse> {
    const accountModel = new Account()
    const personModel = new Person()

    const account = await accountModel.findById(accountId)

    let fullname = ''
    let personId = ''

    if (account.personId) {
      try {
        const personResponse = await personModel.findById(account.personId)
        const personData = personResponse.data

        fullname = 'name' in personData ? personData.name : ''

        personId = account.personId
      } catch {
        // If person not found, use empty string
        fullname = ''
        personId = ''
      }
    }

    return {
      accountId: account.id,
      personId,
      email: account.email,
      fullname,
      roleName: account.role?.name || '',
    }
  }

  public async generateTokenByRefrashToken(
    input: unknown,
  ): Promise<TokenResult> {
    const { refreshToken }: RefreshTokenProps = validateWithZod(
      RefreshTokenSchema,
      input,
    )

    const decoded = decodeRefreshToken(refreshToken)
    const userId = decoded.ref as string
    const tenantId = decoded.tenantId as string

    if (!tenantId) {
      throw new ReferenceError('Refresh token missing tenantId')
    }

    return { token: generateToken(userId, tenantId) }
  }
}
