#!/bin/bash

# Script to launch Prisma Studio for all microservices
# Each instance will run on a different port

set -e

# Define microservices and their ports
declare -A SERVICES=(
    ["ms-account"]="5555"
    ["ms-evaluation"]="5556"
    ["ms-person"]="5557"
    ["ms-purchase"]="5558"
    ["ms-stock"]="5559"
    ["ms-wallet"]="5560"
)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a service directory exists
check_service() {
    local service=$1
    if [ ! -d "$service" ]; then
        print_error "Directory $service not found"
        return 1
    fi
    
    if [ ! -f "$service/package.json" ]; then
        print_error "package.json not found in $service"
        return 1
    fi
    
    if [ ! -d "$service/prisma" ]; then
        print_error "prisma directory not found in $service"
        return 1
    fi
    
    return 0
}

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1
    fi
    return 0
}

# Function to start Prisma Studio for a service
start_prisma_studio() {
    local service=$1
    local port=$2
    
    print_status "Starting Prisma Studio for $service on port $port..."
    
    cd "$service"
    
    # Check if .env file exists
    if [ ! -f ".env" ]; then
        print_warning ".env file not found in $service. Make sure DATABASE_URL is configured."
    fi
    
    # Start Prisma Studio in background
    BROWSER=none yarn prisma studio --port $port > "../prisma-studio-$service.log" 2>&1 &
    local pid=$!
    
    # Store PID for cleanup
    echo $pid > "../.prisma-studio-$service.pid"
    
    cd ..
    
    print_success "Prisma Studio for $service started on port $port (PID: $pid)"
    echo "  Access at: http://localhost:$port"
    echo "  Log file: prisma-studio-$service.log"
}

# Function to stop all Prisma Studio instances
stop_all() {
    print_status "Stopping all Prisma Studio instances..."
    
    for service in "${!SERVICES[@]}"; do
        local pid_file=".prisma-studio-$service.pid"
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if kill -0 $pid 2>/dev/null; then
                kill $pid
                print_success "Stopped Prisma Studio for $service (PID: $pid)"
            fi
            rm -f "$pid_file"
        fi
    done
    
    # Clean up log files
    rm -f prisma-studio-*.log
    
    print_success "All Prisma Studio instances stopped"
}

# Function to show status of all instances
show_status() {
    print_status "Prisma Studio instances status:"
    echo
    
    for service in "${!SERVICES[@]}"; do
        local port=${SERVICES[$service]}
        local pid_file=".prisma-studio-$service.pid"
        
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if kill -0 $pid 2>/dev/null; then
                print_success "$service: Running on port $port (PID: $pid)"
                echo "  Access at: http://localhost:$port"
            else
                print_error "$service: PID file exists but process not running"
                rm -f "$pid_file"
            fi
        else
            print_warning "$service: Not running"
        fi
    done
}

# Main script logic
case "${1:-start}" in
    "start")
        print_status "Starting Prisma Studio for all microservices..."
        echo
        
        # Check all services first
        for service in "${!SERVICES[@]}"; do
            if ! check_service "$service"; then
                print_error "Cannot start Prisma Studio instances due to missing directories or files"
                exit 1
            fi
        done
        
        # Check ports
        for service in "${!SERVICES[@]}"; do
            local port=${SERVICES[$service]}
            if ! check_port $port; then
                print_error "Port $port is already in use (needed for $service)"
                exit 1
            fi
        done
        
        # Start all instances
        for service in "${!SERVICES[@]}"; do
            local port=${SERVICES[$service]}
            start_prisma_studio "$service" "$port"
        done
        
        echo
        print_success "All Prisma Studio instances started!"
        echo
        echo "Access URLs:"
        for service in "${!SERVICES[@]}"; do
            local port=${SERVICES[$service]}
            echo "  $service: http://localhost:$port"
        done
        echo
        echo "To stop all instances, run: $0 stop"
        echo "To check status, run: $0 status"
        ;;
        
    "stop")
        stop_all
        ;;
        
    "status")
        show_status
        ;;
        
    "restart")
        stop_all
        sleep 2
        $0 start
        ;;
        
    *)
        echo "Usage: $0 {start|stop|status|restart}"
        echo
        echo "Commands:"
        echo "  start   - Start Prisma Studio for all microservices"
        echo "  stop    - Stop all Prisma Studio instances"
        echo "  status  - Show status of all instances"
        echo "  restart - Restart all instances"
        echo
        echo "Ports used:"
        for service in "${!SERVICES[@]}"; do
            local port=${SERVICES[$service]}
            echo "  $service: $port"
        done
        exit 1
        ;;
esac
