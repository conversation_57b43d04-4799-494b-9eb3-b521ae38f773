import { useLocation, Outlet, Link } from 'react-router-dom'
import { LoggedPage } from '@thrift/design-system/packages/organisms/LoggedPage'
import { useAuth } from '@app/application/Auth'
import { mockMenu } from '@app/pages/LoggedLayout/mock'

export const LoggedLayout: React.FC = () => {
  const { onLogout } = useAuth()
  const location = useLocation()
  const currentPath = location.pathname

  return (
    <LoggedPage
      userMenu={{
        name: '<PERSON>',
        userFunction: 'Administrador',
        items: [
          {
            name: 'Sair',
            iconName: 'LogOut',
            onClick: () => onLogout(),
          },
        ],
      }}
      menus={mockMenu}
      activePath={currentPath}
      LinkComponent={Link}
    >
      <Outlet />
    </LoggedPage>
  )
}
