import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import '@app/styles/globals.css'

import { ToastProvider } from '@thrift/design-system/packages/molecules/Toast'
import App from '@app/App'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

const queryClient = new QueryClient()

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <ToastProvider>
        <App />
      </ToastProvider>
    </QueryClientProvider>
  </StrictMode>,
)
