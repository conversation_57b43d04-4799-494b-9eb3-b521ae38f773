import { api } from '@app/domain/services'
import type {
  TenantContact,
  TenantContactListParams,
} from '@app/domain/wallet/TenantContact/TenantContact'

export const getTenantContacts = async (params: TenantContactListParams) => {
  const response = await api.get(
    `/wallet/tenant-contacts?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data
}

export const getTenantContactById = async (id: string) => {
  const response = await api.get(`/wallet/tenant-contacts/${id}`)

  return response?.data
}

export const postTenantContact = async (params: Omit<TenantContact, 'id'>) => {
  const response = await api.post('/wallet/tenant-contacts', params)

  return response?.data
}

export const putTenantContact = async (params: TenantContact) => {
  const { id, ...body } = params
  const response = await api.put(`/wallet/tenant-contacts/${id}`, body)

  return response?.data
}

export const deleteTenantContact = async (id: string) => {
  const response = await api.delete(`/wallet/tenant-contacts/${id}`)

  return response?.data
}
