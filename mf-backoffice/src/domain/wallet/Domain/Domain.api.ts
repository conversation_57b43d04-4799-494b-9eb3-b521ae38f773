import { api } from '@app/domain/services'
import type { Domain } from '@app/domain/wallet/Domain/Domain'

export const getDomains = async () => {
  const response = await api.get('/wallet/domains')

  return response?.data
}

export const getDomainById = async (id: string) => {
  const response = await api.get(`/wallet/domains/${id}`)

  return response?.data
}

export const postDomain = async (params: Omit<Domain, 'id'>) => {
  const response = await api.post('/wallet/domains', params)

  return response?.data
}

export const putDomain = async (params: Domain) => {
  const { id, ...body } = params
  const response = await api.put(`/wallet/domains/${id}`, body)

  return response?.data
}

export const deleteDomain = async (id: string) => {
  const response = await api.delete(`/wallet/domains/${id}`)

  return response?.data
}
