import axios, { type AxiosError, type InternalAxiosRequestConfig } from 'axios'
import { useAuthStore } from '@app/domain/stores/Auth'

interface HandledError extends AxiosError {
  isHandled?: boolean
}

const baseURL = 'http://localhost:8080'

export const api = axios.create({
  baseURL,
  headers: { 'Content-Type': 'application/json' },
})

api.interceptors.request.use((config) => {
  const token = useAuthStore.getState().token

  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }

  return config
})

type FailedRequest = {
  resolve: (token: string) => void
  reject: (error: unknown) => void
}

let isRefreshing = false
let failedQueue: FailedRequest[] = []

const processQueue = (error: unknown, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) prom.reject(error)
    else if (token) prom.resolve(token)
  })

  failedQueue = []
}

api.interceptors.response.use(
  (response) => response,

  async (error: HandledError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & {
      _retry?: boolean
    }

    const { refreshToken, setToken } = useAuthStore.getState()

    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      refreshToken
    ) {
      originalRequest._retry = true

      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        })
          .then((newToken) => {
            originalRequest.headers.Authorization = `Bearer ${newToken}`

            return api(originalRequest)
          })
          .catch((err) => Promise.reject(err))
      }

      isRefreshing = true

      try {
        const { data } = await axios.post(`${baseURL}/account/refresh`, {
          refreshToken,
        })

        const newToken = data?.token

        setToken(newToken)

        api.defaults.headers.Authorization = `Bearer ${newToken}`

        processQueue(null, newToken)

        return api(originalRequest)
      } catch (refreshErr) {
        const handledError = refreshErr as HandledError

        handledError.isHandled = true

        processQueue(refreshErr, null)

        return Promise.reject(handledError)
      } finally {
        isRefreshing = false
      }
    }

    return Promise.reject(error)
  },
)
