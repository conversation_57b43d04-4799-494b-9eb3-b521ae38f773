import { api } from '@app/domain/services'
import type { PersonLegal } from '@app/domain/person/PersonLegal/PersonLegal'

export const getPersonLegals = async () => {
  const response = await api.get('/person-legal')

  return response?.data
}

export const getPersonLegalById = async (id: string) => {
  const response = await api.get(`/person-legal/${id}`)

  return response?.data
}

export const getPersonLegalByPersonId = async (personId: string) => {
  const response = await api.get(`/person-legal/personId/${personId}`)

  return response?.data
}

export const postPersonLegal = async (params: Omit<PersonLegal, 'id'>) => {
  const response = await api.post('/person-legal', params)

  return response?.data
}

export const putPersonLegal = async (params: PersonLegal) => {
  const { id, ...body } = params
  const response = await api.put(`/person-legal/${id}`, body)

  return response?.data
}

export const deletePersonLegal = async (id: string) => {
  const response = await api.delete(`/person-legal/${id}`)

  return response?.data
}
