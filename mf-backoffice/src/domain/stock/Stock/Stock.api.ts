import { api } from '@app/domain/services'
import type { Stock, StockListParams } from '@app/domain/stock/Stock/Stock'

export const getStocks = async (params: StockListParams) => {
  const response = await api.get(
    `/stock/stocks?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data
}

export const getStockById = async (id: string) => {
  const response = await api.get(`/stock/stocks/${id}`)

  return response?.data
}

export const postStock = async (params: Omit<Stock, 'id'>) => {
  const response = await api.post('/stock/stocks', params)

  return response?.data
}

export const putStock = async (params: Stock) => {
  const { id, ...body } = params
  const response = await api.put(`/stock/stocks/${id}`, body)

  return response?.data
}

export const deleteStock = async (id: string) => {
  const response = await api.delete(`/stock/stocks/${id}`)

  return response?.data
}
