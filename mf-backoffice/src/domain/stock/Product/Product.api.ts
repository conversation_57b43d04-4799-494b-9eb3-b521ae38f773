import { api } from '@app/domain/services'
import type {
  Product,
  ProductListParams,
} from '@app/domain/stock/Product/Product'

export const getProducts = async (params: ProductListParams) => {
  const response = await api.get(
    `/stock/product?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data
}

export const getProductById = async (id: string) => {
  const response = await api.get(`/stock/product/${id}`)

  return response?.data
}

export const postProduct = async (params: Omit<Product, 'id'>) => {
  const response = await api.post('/stock/product', params)

  return response?.data
}

export const putProduct = async (params: Product) => {
  const { id, ...body } = params
  const response = await api.put(`/stock/product/${id}`, body)

  return response?.data
}

export const deleteProduct = async (id: string) => {
  const response = await api.delete(`/stock/product/${id}`)

  return response?.data
}
