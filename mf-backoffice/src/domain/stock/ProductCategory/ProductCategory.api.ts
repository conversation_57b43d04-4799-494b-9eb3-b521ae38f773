import { api } from '@app/domain/services'
import type {
  ProductCategory,
  ProductCategoryListParams,
} from '@app/domain/stock/ProductCategory/ProductCategory'

export const getProductCategories = async (
  params: ProductCategoryListParams,
) => {
  const response = await api.get(
    `/stock/product-categories?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data
}

export const getProductCategoryById = async (id: string) => {
  const response = await api.get(`/stock/product-categories/${id}`)

  return response?.data
}

export const postProductCategory = async (
  params: Omit<ProductCategory, 'id'>,
) => {
  const response = await api.post('/stock/product-categories', params)

  return response?.data
}

export const putProductCategory = async (params: ProductCategory) => {
  const { id, ...body } = params
  const response = await api.put(`/stock/product-categories/${id}`, body)

  return response?.data
}

export const deleteProductCategory = async (id: string) => {
  const response = await api.delete(`/stock/product-categories/${id}`)

  return response?.data
}
