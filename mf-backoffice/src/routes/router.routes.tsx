import { createBrowserRouter, Navigate } from 'react-router-dom'

import { PrivateRoute } from '@app/pages/PrivateRoute'
import { LoggedLayout } from '@app/pages/LoggedLayout'

import { Dashboard } from '@app/pages/Dashboard'
import { Stock } from '@app/pages/Stock'
import { Catalog } from '@app/pages/Catalog'
import { Grid } from '@app/pages/Grid'
import { Categories } from '@app/pages/Categories'
import { Users } from '@app/pages/Users'
import { Units } from '@app/pages/Units'

export const router = createBrowserRouter(
  [
    {
      element: <PrivateRoute />,
      children: [
        {
          element: <LoggedLayout />,
          children: [
            { path: '/', element: <Dashboard /> },
            { path: '/stock', element: <Stock /> },
            { path: '/catalog', element: <Catalog /> },
            { path: '/grid', element: <Grid /> },
            { path: '/categories', element: <Categories /> },
            { path: '/users', element: <Users /> },
            { path: '/units', element: <Units /> },
          ],
        },
      ],
    },
    {
      path: '*',
      element: <Navigate to="/backoffice" replace />,
    },
  ],
  {
    basename: '/backoffice',
  },
)
