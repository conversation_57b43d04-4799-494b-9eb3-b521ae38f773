{"name": "@thrift/mf-backoffice", "version": "0.1.0", "private": true, "description": "Backoffice web page & application", "author": "Thrift Technology <<EMAIL>>", "type": "module", "repository": {"type": "git", "url": "https://github.com/thrift-technology/mf-backoffice.git"}, "scripts": {"dev": "yarn healthcheck && vite", "build": "yarn healthcheck && tsc -b && vite build", "lint": "eslint .", "lint:fix": "yarn lint --fix", "format": "prettier --check .", "format:fix": "prettier --write .", "typecheck": "tsc --noEmit", "healthcheck": "yarn lint && yarn format && yarn typecheck", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "5.1.1", "@tanstack/react-query": "5.83.0", "@thrift/design-system": "https://github.com/thrift-technology/design-system.git#main", "axios": "1.11.0", "i18next": "25.3.2", "i18next-browser-languagedetector": "8.2.0", "lucide-react": "0.525.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.61.1", "react-i18next": "15.6.1", "react-router-dom": "7.7.1", "zod": "4.0.9", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "9.31.0", "@tailwindcss/vite": "4.1.11", "@types/node": "24.1.0", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@vitejs/plugin-react": "4.7.0", "eslint": "9.31.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-prettier": "5.5.3", "eslint-plugin-react-hooks": "6.0.0", "eslint-plugin-react-refresh": "0.4.20", "eslint-plugin-unused-imports": "4.1.4", "globals": "16.3.0", "prettier": "3.6.2", "tailwindcss": "4.1.11", "typescript": "5.8.3", "typescript-eslint": "8.38.0", "vite": "7.0.6"}}