import { PrismaClient } from '@prisma/client'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function () {
  await prisma.tenant.upsert({
    where: { tenantUid: 'thrift' },
    update: {},
    create: {
      id: 'c597c17c-b077-4fd7-a3b1-516b2541dceb',
      name: 'Thrift',
      tenantUid: 'thrift',
      balance: 1000.0,
    },
  })

  await prisma.tenant.upsert({
    where: { tenantUid: 'thrift-dev' },
    update: {},
    create: {
      id: '1ea5fbde-c633-464f-aef3-6250743693b2',
      name: 'Thrift Dev',
      tenantUid: 'thrift-dev',
      balance: 530.0,
    },
  })
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
