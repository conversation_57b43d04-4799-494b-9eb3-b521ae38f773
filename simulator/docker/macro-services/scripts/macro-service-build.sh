#!/bin/bash
set -eux

echo "====> Initializing variables"
_storageFilename="${STORAGE_DIR}/${APP_NAME}"

echo "====> Removing previous repository and storage data"
rm -rf "${REPOSITORY_NAME}" "${_storageFilename}"

echo "====> Setting up GitHub credentials"
git config --global url."https://$<EMAIL>/thrift-technology/".insteadOf "https://github.com/thrift-technology/"

echo "====> Cloning repository"
git clone "https://github.com/thrift-technology/${APP_NAME}.git" "${REPOSITORY_NAME}"

echo "====> Copying and provisioning app.config"
cp "${REPOSITORY_NAME}/app.config.sample.json" "${REPOSITORY_NAME}/app.config.json"
cd "${REPOSITORY_NAME}"
/bin/bash .scripts/provisioning.sh
cd ..

echo "====> Installing & building"
cd "${REPOSITORY_NAME}"
yarn cache clean
if ! yarn install --immutable; then
  echo "----> Strict install failed, trying non-immutable"
  yarn cache clean
  yarn install
fi
yarn build
cd ..

echo "====> Checking tsconfig.json and extracting build directory"
_tsconfigFilename="${REPOSITORY_NAME}/tsconfig.json"
if [ ! -f "$_tsconfigFilename" ]; then
  echo "[ERROR] '${_tsconfigFilename}' file not found"
  exit 1
fi
_builtDir=$(node -e "console.log(require('./${_tsconfigFilename}').compilerOptions.outDir)")

echo "====> Installing production dependencies only"
cd "${REPOSITORY_NAME}"
rm -rf node_modules
yarn cache clean
if ! yarn install --production --immutable; then
  echo "----> Strict production install failed, trying non-immutable"
  yarn install --production
fi
cd ..

echo "====> Copying build output and node_modules to service directory"
mkdir -p "${_storageFilename}"
cp -r "${REPOSITORY_NAME}/node_modules" "${_storageFilename}/node_modules"
cp -r "${REPOSITORY_NAME}/${_builtDir}" "${_storageFilename}/${_builtDir}"

echo "====> Copying PEM credentials if available"
_cred_dir="${REPOSITORY_NAME}/.credentials"
if [ -d "$_cred_dir" ] && find "$_cred_dir" -maxdepth 1 -type f -name "*.pem" | grep -q .; then
  mkdir -p "${_storageFilename}/${_builtDir}/.credentials"
  cp -r "$_cred_dir"/*.pem "${_storageFilename}/${_builtDir}/.credentials/"
fi

echo "====> Build and copy completed successfully"
exit 0
