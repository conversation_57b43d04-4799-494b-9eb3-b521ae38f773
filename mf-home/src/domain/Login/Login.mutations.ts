import { useCallback } from 'react'
import { useMutation } from '@tanstack/react-query'
import {
  postLogin,
  type LoginParams,
  type LoginResponse,
} from '@app/domain/Login'

export const useLoginMutation = () => {
  const { mutateAsync, isPending } = useMutation<
    LoginResponse,
    Error,
    LoginParams
  >({
    mutationFn: postLogin,
  })

  const postLoginAsync = useCallback(
    async (props: LoginParams): Promise<LoginResponse> => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    postLoginAsync,
    loading: isPending,
  }
}
