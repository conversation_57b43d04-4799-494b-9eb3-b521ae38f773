import type { LoginParams, LoginResponse } from '@app/domain/Login'
import { api, type ApiResponse } from '@app/domain/services'
import { useAuthStore } from '@app/domain/stores/Auth'

export const postLogin = async (
  params: LoginParams,
): Promise<LoginResponse> => {
  const response = await api.post<ApiResponse<LoginResponse>>(
    '/account/authenticate',
    params,
  )

  const headers = response.headers

  let token = headers['authorization'] || headers['Authorization']
  const refreshToken = headers['x-refresh-token'] || headers['X-Refresh-Token']

  if (typeof token === 'string' && token.startsWith('Bearer ')) {
    token = token.split(' ')[1]
  }

  useAuthStore.getState().setToken(token)
  useAuthStore.getState().setRefreshToken(refreshToken)

  return response.data.data
}
