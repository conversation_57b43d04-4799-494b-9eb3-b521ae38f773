import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { type LoginSchemaType, useLoginSchema } from '@app/application/Login'
import { useLoginMutation } from '@app/domain/Login'
import { useToast } from '@thrift/design-system/packages/molecules/Toast'
import { useLanguage } from '@app/application/Language'
import { useAuthStore } from '@app/domain/stores/Auth'
import { useEffect } from 'react'

export const useLogin = () => {
  const { showToast } = useToast()
  const { translate } = useLanguage()
  const LoginSchema = useLoginSchema()
  const token = useAuthStore((state) => state.token)

  useEffect(() => {
    if (token) {
      window.location.href = '/backoffice'
    }
  }, [token])

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginSchemaType>({
    resolver: zodResolver(LoginSchema),
  })

  const { postLoginAsync, loading } = useLoginMutation()

  const onSubmit = async (params: LoginSchemaType) => {
    try {
      const data = await postLoginAsync(params)

      useAuthStore.getState().setUser(data)
      showToast({
        title: translate('login:loginSuccessTitle'),
        message: translate('login:loginSuccessMessage', {
          email: data.email,
        }),
        type: 'success',
      })

      window.location.href = '/backoffice'
    } catch {
      showToast({
        title: translate('login:loginFailedTitle'),
        message: translate('login:loginFailedMessage'),
        type: 'error',
      })
    }
  }

  return {
    handleSubmit: handleSubmit(onSubmit),
    register,
    errors,
    loading,
  }
}
